<template>
  <div class="space-y-6">
    <div class="flex justify-between items-center">
      <div class="flex items-center space-x-6">
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">游戏服务管理</h1>

        <!-- 服务器类型选择 -->
        <div class="flex border-b border-gray-200 dark:border-gray-700">
          <button
            @click="switchServer(SERVER_TYPE.PRODUCTION)"
            class="px-4 py-2 border-b-2 font-medium text-sm"
            :class="currentServerType === SERVER_TYPE.PRODUCTION
              ? 'border-primary-500 text-primary-600 dark:text-primary-400'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'"
          >
            正式服
          </button>
          <button
            @click="switchServer(SERVER_TYPE.TEST)"
            class="px-4 py-2 border-b-2 font-medium text-sm"
            :class="currentServerType === SERVER_TYPE.TEST
              ? 'border-primary-500 text-primary-600 dark:text-primary-400'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'"
          >
            测试服
          </button>
        </div>

        <div class="text-sm text-gray-500 dark:text-gray-400">
          {{ currentServer.baseUrl }}
        </div>
      </div>

      <div class="flex space-x-4">
        <button
          @click="refreshData"
          class="btn-secondary flex items-center space-x-2"
          :disabled="loading || roomsLoading || gatewayLoading"
        >
          <ArrowPathIcon class="h-5 w-5" :class="{ 'animate-spin': loading || roomsLoading || gatewayLoading }" />
          <span>刷新所有数据</span>
        </button>
      </div>
    </div>

    <!-- 数据概览卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div class="flex items-center justify-between">
          <h2 class="text-lg font-medium text-gray-900 dark:text-white">在线玩家</h2>
          <UsersIcon class="h-8 w-8 text-primary-500" />
        </div>
        <p class="mt-2 text-3xl font-bold text-gray-900 dark:text-white">
          {{ loading ? '加载中...' : playersCount }}
        </p>
        <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">当前在线玩家总数</p>
      </div>

      <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div class="flex items-center justify-between">
          <h2 class="text-lg font-medium text-gray-900 dark:text-white">活跃房间</h2>
          <HomeIcon class="h-8 w-8 text-primary-500" />
        </div>
        <p class="mt-2 text-3xl font-bold text-gray-900 dark:text-white">
          {{ loading || roomsLoading ? '加载中...' : roomsCount }}
        </p>
        <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">当前活跃房间数量</p>
      </div>

      <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div class="flex items-center justify-between">
          <h2 class="text-lg font-medium text-gray-900 dark:text-white">网关连接</h2>
          <ServerIcon class="h-8 w-8 text-primary-500" />
        </div>
        <p class="mt-2 text-3xl font-bold text-gray-900 dark:text-white">
          {{ loading || gatewayLoading ? '加载中...' : gatewayClientsCount }}
        </p>
        <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">当前网关连接数</p>
      </div>
    </div>

    <!-- 在线人数统计图表 -->
    <PlayersChart
      :data="playersHistoryData"
      :period="selectedPeriod"
      :loading="playersHistoryLoading"
      :current-value="playersHistoryCurrentValue"
      :change="playersHistoryChange"
      @period-change="handlePeriodChange"
      @refresh="refreshPlayersHistory"
    />

    <!-- 标签页 -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow">
      <div class="border-b border-gray-200 dark:border-gray-700">
        <nav class="flex -mb-px">
          <button
            @click="activeTab = 'rooms'"
            class="px-6 py-3 border-b-2 font-medium text-sm"
            :class="activeTab === 'rooms'
              ? 'border-primary-500 text-primary-600 dark:text-primary-400'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'"
          >
            房间列表
          </button>
          <button
            @click="activeTab = 'gateway'"
            class="px-6 py-3 border-b-2 font-medium text-sm"
            :class="activeTab === 'gateway'
              ? 'border-primary-500 text-primary-600 dark:text-primary-400'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'"
          >
            网关用户
          </button>
        </nav>
      </div>
      <div class="p-6">
        <!-- 房间列表 -->
        <div v-if="activeTab === 'rooms'">
          <div class="flex justify-end items-center mb-4">
            <button
              @click="refreshRooms"
              class="btn-secondary flex items-center space-x-2 py-1 px-3"
              :disabled="roomsLoading"
            >
              <ArrowPathIcon class="h-4 w-4" :class="{ 'animate-spin': roomsLoading }" />
              <span>刷新房间</span>
            </button>
          </div>
          <RoomList
            :rooms="rooms"
            :loading="roomsLoading"
            @view-room="viewRoom"
          />
        </div>

        <!-- 网关用户列表 -->
        <div v-if="activeTab === 'gateway'">
          <div class="flex justify-between items-center mb-4">
            <h2 class="text-lg font-medium text-gray-900 dark:text-white">网关用户列表</h2>
            <button
              @click="refreshGatewayClients"
              class="btn-secondary flex items-center space-x-2 py-1 px-3"
              :disabled="gatewayLoading"
            >
              <ArrowPathIcon class="h-4 w-4" :class="{ 'animate-spin': gatewayLoading }" />
              <span>刷新网关</span>
            </button>
          </div>
          <PlayerList
            :players="gatewayClients"
            :loading="gatewayLoading"
            title=""
          />
        </div>
      </div>
    </div>

    <!-- 房间详情弹窗 -->
    <el-dialog
      v-model="showRoomDetail"
      title="房间详情"
      width="80%"
      destroy-on-close
    >
      <RoomDetail
        v-if="showRoomDetail"
        :room="selectedRoom"
        :loading="roomDetailLoading"
      />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElDialog } from 'element-plus'
import {
  ArrowPathIcon,
  UsersIcon,
  HomeIcon,
  ServerIcon
} from '@heroicons/vue/24/outline'
import { gameService } from '../../services/gameService'
import { schedulerService } from '../../services/schedulerService'
import { SERVER_TYPE } from '../../services/dbService'
import RoomList from './RoomList.vue'
import RoomDetail from './RoomDetail.vue'
import PlayerList from './PlayerList.vue'
import PlayersChart from './PlayersChart.vue'

// 状态
const loading = ref(false)
const roomsLoading = ref(false)
const gatewayLoading = ref(false)
const roomDetailLoading = ref(false)
const playersHistoryLoading = ref(false)
const rooms = ref([])
const gatewayClients = ref([])
const playersCount = ref(0)
const activeTab = ref('rooms')
const showRoomDetail = ref(false)
const selectedRoom = ref(null)

// 服务器类型相关状态
const currentServerType = ref(SERVER_TYPE.PRODUCTION)
const currentServer = ref(gameService.getCurrentServer())

// 在线人数统计相关状态
const playersHistoryData = ref([])
const selectedPeriod = ref('24h')
const playersHistoryCurrentValue = ref(0)
const playersHistoryChange = ref(0)

// 可用的时间范围选项
const availablePeriods = ['1h', '6h', '24h', '3d', '7d', '14d', '30d', '90d']

// 计算属性
const roomsCount = computed(() => rooms.value.length)
const gatewayClientsCount = computed(() => gatewayClients.value.length)

// 数据收集间隔（15分钟）
const DATA_COLLECTION_INTERVAL = 15 * 60 * 1000

// 切换服务器
const switchServer = async (serverType) => {
  if (currentServerType.value === serverType) {
    return // 如果是当前服务器，不做任何操作
  }

  loading.value = true
  try {
    // 切换服务器
    const success = gameService.switchServerType(serverType)
    if (success) {
      currentServerType.value = serverType
      currentServer.value = gameService.getCurrentServer()
      ElMessage.success(`已切换到${currentServer.value.name}`)

      // 刷新数据
      await refreshData()
      await fetchPlayersHistory(selectedPeriod.value)
    } else {
      ElMessage.error('切换服务器失败')
    }
  } catch (error) {
    console.error('切换服务器失败:', error)
    ElMessage.error('切换服务器失败')
  } finally {
    loading.value = false
  }
}

// 初始化
onMounted(async () => {
  // 设置初始服务器类型
  currentServerType.value = gameService.getCurrentServerType()

  // 刷新初始数据
  await refreshData()
  await fetchPlayersHistory(selectedPeriod.value)

  // 启动数据收集任务
  schedulerService.startDataCollection(DATA_COLLECTION_INTERVAL)

  // 延迟一秒后触发数据收集，避免与其他初始化操作冲突
  setTimeout(async () => {
    try {
      await schedulerService.triggerDataCollection()
      console.log('初始数据收集完成')
    } catch (error) {
      console.error('初始数据收集失败:', error)
    }
  }, 1000)
})

// 组件卸载时清理
onUnmounted(() => {
  // 停止数据收集任务
  schedulerService.stopDataCollection()
})

// 刷新所有数据
const refreshData = async () => {
  loading.value = true
  roomsLoading.value = true
  gatewayLoading.value = true

  try {
    // 并行请求数据
    const [roomsData, gatewayData, countData] = await Promise.all([
      gameService.getAllRooms(),
      gameService.getGatewayClients(),
      gameService.getPlayersCount()
    ])

    console.log('房间数据:', roomsData)
    console.log('网关数据:', gatewayData)
    console.log('玩家数量数据:', countData)

    // 处理数据，确保数据格式正确
    rooms.value = Array.isArray(roomsData) ? roomsData : []
    gatewayClients.value = Array.isArray(gatewayData) ? gatewayData : []
    playersCount.value = countData && typeof countData.count === 'number' ? countData.count :
                        (typeof countData === 'number' ? countData : 0)

    ElMessage.success('数据刷新成功')
  } catch (error) {
    console.error('刷新所有数据失败:', error)
    ElMessage.error('刷新数据失败')
  } finally {
    loading.value = false
    roomsLoading.value = false
    gatewayLoading.value = false
  }
}

// 获取历史在线人数数据
const fetchPlayersHistory = async (period) => {
  playersHistoryLoading.value = true

  try {
    // 首先检查API是否可用
    const apiAvailable = await dbService.checkApiAvailability(currentServerType.value)
    if (!apiAvailable) {
      console.warn(`${currentServerType.value}服务器API不可用，可能使用本地存储数据`)
      ElMessage.warning(`${currentServer.value.name}服务器API不可用，将使用本地存储数据`)
    }

    const historyData = await gameService.getPlayersHistory(period)
    console.log('历史在线人数数据:', historyData)

    if (historyData && historyData.data && Array.isArray(historyData.data)) {
      playersHistoryData.value = historyData.data
      playersHistoryCurrentValue.value = historyData.currentValue || 0

      // 确保 change 是数字类型
      if (typeof historyData.change === 'string') {
        playersHistoryChange.value = Number(historyData.change)
      } else {
        playersHistoryChange.value = historyData.change || 0
      }

      console.log('处理后的变化百分比:', playersHistoryChange.value, '类型:', typeof playersHistoryChange.value)
    } else {
      console.warn('历史在线人数数据格式不符合预期:', historyData)
      playersHistoryData.value = []
      playersHistoryCurrentValue.value = 0
      playersHistoryChange.value = 0
    }
  } catch (error) {
    console.error('获取历史在线人数数据失败:', error)
    ElMessage.error('获取历史在线人数数据失败')
    playersHistoryData.value = []
    playersHistoryCurrentValue.value = 0
    playersHistoryChange.value = 0
  } finally {
    playersHistoryLoading.value = false
  }
}

// 处理时间段变更
const handlePeriodChange = (period) => {
  // 验证时间段是否有效
  if (availablePeriods.includes(period)) {
    selectedPeriod.value = period
    fetchPlayersHistory(period)
  } else {
    console.warn(`无效的时间段: ${period}，使用默认值: 24h`)
    selectedPeriod.value = '24h'
    fetchPlayersHistory('24h')
  }
}

// 刷新在线人数历史数据
const refreshPlayersHistory = async () => {
  // 先触发一次数据收集
  try {
    await schedulerService.triggerDataCollection()
    console.log(`手动数据收集完成，服务器类型: ${currentServerType.value}`)

    // 然后刷新图表数据
    await fetchPlayersHistory(selectedPeriod.value)

    ElMessage.success(`${currentServer.value.name}在线人数数据已更新`)
  } catch (error) {
    console.error(`刷新${currentServer.value.name}在线人数数据失败:`, error)
    ElMessage.error(`刷新${currentServer.value.name}在线人数数据失败`)
  }
}

// 刷新房间数据
const refreshRooms = async () => {
  roomsLoading.value = true

  try {
    // 请求房间数据
    const roomsData = await gameService.getAllRooms()
    console.log('房间数据:', roomsData)

    // 处理数据，确保数据格式正确
    rooms.value = Array.isArray(roomsData) ? roomsData : []

    ElMessage.success('房间数据刷新成功')
  } catch (error) {
    console.error('刷新房间数据失败:', error)
    ElMessage.error('刷新房间数据失败')
  } finally {
    roomsLoading.value = false
  }
}

// 刷新网关用户数据
const refreshGatewayClients = async () => {
  gatewayLoading.value = true

  try {
    // 请求网关用户数据
    const gatewayData = await gameService.getGatewayClients()
    console.log('网关数据:', gatewayData)

    // 处理数据，确保数据格式正确
    gatewayClients.value = Array.isArray(gatewayData) ? gatewayData : []

    ElMessage.success('网关用户数据刷新成功')
  } catch (error) {
    console.error('刷新网关用户数据失败:', error)
    ElMessage.error('刷新网关用户数据失败')
  } finally {
    gatewayLoading.value = false
  }
}

// 查看房间详情
const viewRoom = async (room) => {
  selectedRoom.value = room
  showRoomDetail.value = true
  roomDetailLoading.value = true

  try {
    // 获取详细信息
    const roomDetail = await gameService.getRoomById(room.id)
    console.log('房间详情数据:', roomDetail)

    // 确保 roomDetail 是对象
    if (roomDetail && typeof roomDetail === 'object') {
      selectedRoom.value = { ...room, ...roomDetail }
    } else {
      selectedRoom.value = { ...room }
    }

    // 如果房间有id，获取玩家信息
    if (room.id) {
      try {
        const playersData = await gameService.getGamePlayers(room.id)
        console.log('房间玩家数据:', playersData)

        // 确保 playersData 是数组
        selectedRoom.value.players = Array.isArray(playersData) ? playersData : []
      } catch (error) {
        console.error(`获取房间 ${room.id} 玩家信息失败:`, error)
        selectedRoom.value.players = []
      }
    }
  } catch (error) {
    console.error('获取房间详情失败:', error)
    ElMessage.error('获取房间详情失败')
  } finally {
    roomDetailLoading.value = false
  }
}
</script>
