<template>
  <div class="tree-node">
    <!-- 节点内容 -->
    <div class="node-content" :style="{ paddingLeft: `${level * 16}px` }">
      <!-- 展开/折叠按钮 -->
      <button 
        v-if="node.children.length > 0"
        @click="toggleExpanded"
        class="expand-button"
      >
        <span :class="{ 'rotate-90': isExpanded }">▶</span>
      </button>
      <span v-else class="expand-placeholder"></span>
      
      <!-- 节点图标 -->
      <span class="node-icon">{{ getNodeIcon(node.type) }}</span>
      
      <!-- 节点名称和类型 -->
      <div class="node-info">
        <span class="node-name">{{ node.name }}</span>
        <span class="node-type">({{ node.type }})</span>
      </div>
      
      <!-- 属性控制按钮 -->
      <div class="node-controls">
        <!-- 可见性切换 -->
        <button
          @click="$emit('toggle-visibility', node.id)"
          class="control-btn"
          :class="{ active: node.visible }"
          title="切换可见性"
        >
          👁
        </button>
        
        <!-- 投射阴影切换（仅对Mesh有效） -->
        <button
          v-if="node.type === 'Mesh'"
          @click="$emit('toggle-cast-shadow', node.id)"
          class="control-btn"
          :class="{ active: node.castShadow }"
          title="切换投射阴影"
        >
          🌞
        </button>
        
        <!-- 接收阴影切换（仅对Mesh有效） -->
        <button
          v-if="node.type === 'Mesh'"
          @click="$emit('toggle-receive-shadow', node.id)"
          class="control-btn"
          :class="{ active: node.receiveShadow }"
          title="切换接收阴影"
        >
          🌑
        </button>
      </div>
    </div>
    
    <!-- 子节点 -->
    <div v-if="isExpanded && node.children.length > 0" class="children">
      <SceneTreeNode
        v-for="child in node.children"
        :key="child.id"
        :node="child"
        :level="level + 1"
        @toggle-visibility="$emit('toggle-visibility', $event)"
        @toggle-cast-shadow="$emit('toggle-cast-shadow', $event)"
        @toggle-receive-shadow="$emit('toggle-receive-shadow', $event)"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits } from 'vue'
import type { SceneNodeInfo } from '../../services/modelEditorService'

// Props
defineProps<{
  node: SceneNodeInfo
  level: number
}>()

// Events
defineEmits<{
  'toggle-visibility': [nodeId: string]
  'toggle-cast-shadow': [nodeId: string]
  'toggle-receive-shadow': [nodeId: string]
}>()

// 状态
const isExpanded = ref(true) // 默认展开

// 方法
const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value
}

const getNodeIcon = (type: string): string => {
  switch (type) {
    case 'Mesh': return '🔷'
    case 'Group': return '📁'
    case 'Scene': return '🌍'
    case 'Object3D': return '📦'
    case 'Light': return '💡'
    case 'Camera': return '📷'
    case 'Bone': return '🦴'
    case 'SkinnedMesh': return '🎭'
    default: return '❓'
  }
}
</script>

<style scoped>
.tree-node {
  @apply text-sm;
}

.node-content {
  @apply flex items-center py-1 px-2 rounded transition-colors hover:bg-white hover:bg-opacity-10;
}

.expand-button {
  @apply w-4 h-4 flex items-center justify-center text-xs text-gray-300 hover:text-white transition-transform;
}

.expand-placeholder {
  @apply w-4 h-4;
}

.node-icon {
  @apply mx-2 text-base;
}

.node-info {
  @apply flex-1 min-w-0;
}

.node-name {
  @apply text-white font-medium;
}

.node-type {
  @apply text-gray-400 text-xs ml-1;
}

.node-controls {
  @apply flex gap-1 ml-2;
}

.control-btn {
  @apply w-6 h-6 flex items-center justify-center text-xs rounded transition-colors border border-gray-600;
  background: rgba(255, 255, 255, 0.1);
}

.control-btn:hover {
  @apply bg-white bg-opacity-20;
}

.control-btn.active {
  @apply bg-green-500 bg-opacity-50 border-green-400;
}

.children {
  @apply border-l border-gray-600 ml-2;
}

.rotate-90 {
  transform: rotate(90deg);
}
</style>
