<template>
  <div class="scene-tree-panel">
    <h3 class="text-lg font-semibold mb-4 text-white">场景结构</h3>
    
    <div v-if="!sceneStructure" class="no-structure">
      没有可用的场景结构
    </div>
    
    <div v-else class="tree-container">
      <SceneTreeNode 
        :node="sceneStructure" 
        :level="0"
        @toggle-visibility="$emit('toggle-visibility', $event)"
        @toggle-cast-shadow="$emit('toggle-cast-shadow', $event)"
        @toggle-receive-shadow="$emit('toggle-receive-shadow', $event)"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue'
import type { SceneNodeInfo } from '../../services/modelEditorService'
import SceneTreeNode from './SceneTreeNode.vue'

// Props
defineProps<{
  sceneStructure: SceneNodeInfo | null
}>()

// Events
defineEmits<{
  'toggle-visibility': [nodeId: string]
  'toggle-cast-shadow': [nodeId: string]
  'toggle-receive-shadow': [nodeId: string]
}>()
</script>

<style scoped>
.scene-tree-panel {
  @apply bg-black bg-opacity-70 text-white p-4 rounded w-80 max-w-sm overflow-y-auto shadow-lg;
  max-height: 80vh;
}

.no-structure {
  @apply italic text-center text-gray-300;
}

.tree-container {
  @apply space-y-1;
}
</style>
