<template>
  <div class="space-y-4">
    <!-- 面包屑导航 -->
    <nav class="flex items-center space-x-2 text-sm">
      <div class="flex items-center space-x-2">
        <!-- 返回上一级按钮 -->
        <button 
          class="flex items-center px-2 py-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
          @click="navigateBack"
          :disabled="!currentPath"
          :class="{'opacity-50 cursor-not-allowed': !currentPath}"
          title="返回上一级"
        >
          <ArrowLeftIcon class="h-5 w-5 text-primary-600" />
        </button>
        <!-- 根目录按钮 -->
        <button 
          class="flex items-center px-2 py-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
          @click="navigateToRoot"
          :disabled="!currentPath"
          :class="{'opacity-50 cursor-not-allowed': !currentPath}"
          title="返回根目录"
        >
          <HomeIcon class="h-5 w-5 text-primary-600" />
        </button>
      </div>

      <!-- 路径导航 - 添加aria-label提高可访问性 -->
      <div class="flex items-center space-x-2 text-gray-600 dark:text-gray-400" aria-label="文件路径导航">
        <button 
          class="px-2 py-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
          @click="navigateToRoot"
          aria-label="导航到根目录"
        >
          根目录
        </button>
        <template v-for="(part, index) in pathParts" :key="index">
          <ChevronRightIcon class="h-4 w-4" />
          <button 
            @click="navigateTo(index)"
            class="px-2 py-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
          >
            {{ part }}
          </button>
        </template>
      </div>
    </nav>

    <!-- 操作栏改进 -->
    <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0">
      <div class="flex flex-wrap gap-2">
        <button 
          class="btn-secondary flex items-center space-x-2" 
          @click="toggleSelectMode"
        >
          <CheckCircleIcon class="h-5 w-5" :class="{'text-primary-600': isSelecting}" />
          <span>{{ isSelecting ? '取消多选' : '多选' }}</span>
        </button>
        <button 
          v-if="isSelecting"
          class="btn-danger flex items-center space-x-2" 
          @click="confirmBatchDelete"
          :disabled="selectedItems.size === 0"
          :class="{'opacity-50 cursor-not-allowed': selectedItems.size === 0}"
        >
          <TrashIcon class="h-5 w-5" />
          <span>删除选中项 ({{ selectedItems.size }})</span>
        </button>
        <button 
          class="btn-primary flex items-center space-x-2" 
          @click="showNewFolderModal = true"
        >
          <FolderPlusIcon class="h-5 w-5" />
          <span>新建文件夹</span>
        </button>
        <label class="btn-primary flex items-center space-x-2 cursor-pointer">
          <ArrowUpTrayIcon class="h-5 w-5" />
          <span>上传文件</span>
          <input 
            type="file" 
            class="hidden" 
            @change="handleFileUpload" 
            multiple 
            :disabled="loading"
          />
        </label>
        <button 
          v-if="isSelecting"
          class="btn-primary flex items-center space-x-2" 
          @click="showCopyModal = true"
          :disabled="selectedItems.size === 0"
          :class="{'opacity-50 cursor-not-allowed': selectedItems.size === 0}"
        >
          <DocumentDuplicateIcon class="h-5 w-5" />
          <span>复制到新文件夹</span>
        </button>
      </div>
      <div class="relative">
        <input
          type="text"
          placeholder="搜索文件..."
          class="input pl-10"
          v-model="searchQuery"
          @input="fileState.value.searchQuery = searchQuery"
        />
        <MagnifyingGlassIcon class="h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
      </div>
    </div>

    <!-- 文件列表改进 - 添加aria属性提高可访问性 -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
      <div class="min-h-[400px]" role="region" aria-label="文件列表">
        <!-- 空状态显示 -->
        <div 
          v-if="!loading && filteredItems.length === 0" 
          class="h-[400px] flex flex-col items-center justify-center text-gray-500 dark:text-gray-400"
        >
          <FolderOpenIcon class="h-16 w-16 mb-4" />
          <p v-if="searchQuery">没有找到匹配的文件</p>
          <p v-else>此文件夹为空</p>
        </div>

        <!-- 文件列表 - 添加键盘导航支持 -->
        <ul v-else class="divide-y divide-gray-200 dark:divide-gray-700" role="list">
          <li
            v-for="item in filteredItems"
            :key="item.name"
            class="group hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150"
            tabindex="0"
            @keydown.enter="isSelecting ? toggleSelection(item) : (item.type === 'directory' ? navigateToFolder(item) : previewFile(item))"
          >
            <div class="px-4 py-3 flex items-center justify-between">
              <div class="flex items-center space-x-3">
                <div v-if="isSelecting" class="flex-shrink-0">
                  <input
                    type="checkbox"
                    class="h-4 w-4 rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                    :checked="selectedItems.has(item)"
                    @change="toggleSelection(item)"
                  />
                </div>
                <div 
                  class="flex items-center space-x-3 cursor-pointer"
                  @click="isSelecting ? toggleSelection(item) : (item.type === 'directory' ? navigateToFolder(item) : previewFile(item))"
                >
                  <FolderIcon 
                    v-if="item.type === 'directory'" 
                    class="h-6 w-6 text-primary-500" 
                  />
                  <DocumentIcon 
                    v-else 
                    class="h-6 w-6 text-gray-400"
                  />
                  <div class="flex items-center">
                    <span class="text-gray-900 dark:text-gray-100 font-medium">{{ item.name }}</span>
                    <span
                      v-if="item.type === 'directory'"
                      class="inline-flex items-center px-2 py-0.5 ml-2 rounded-full bg-primary-50 text-primary-700 text-xs font-semibold shadow-sm dir-badge"
                      :title="dirCounts[item.path] ? `${dirCounts[item.path].dirCount} 个文件夹 / ${dirCounts[item.path].fileCount} 个文件` : '统计中...'"
                    >
                      <FolderIcon class="w-3 h-3 mr-1 text-primary-400" />
                      {{ dirCounts[item.path]?.dirCount ?? '-' }}
                      <span class="mx-1 text-gray-300">/</span>
                      <DocumentIcon class="w-3 h-3 mr-1 text-gray-400" />
                      {{ dirCounts[item.path]?.fileCount ?? '-' }}
                    </span>
                  </div>
                </div>
              </div>
              
              <!-- 操作按钮在多选模式下隐藏 -->
              <div v-if="!isSelecting" class="flex items-center space-x-2 opacity-0 group-hover:opacity-100 transition-opacity">
                <button
                  v-if="item.type !== 'directory'"
                  class="p-2 rounded-lg text-gray-400 hover:text-primary-600 dark:hover:text-primary-400"
                  @click="downloadFile(item)"
                  title="下载"
                >
                  <ArrowDownTrayIcon class="h-5 w-5" />
                </button>
                <button
                  class="p-2 rounded-lg text-gray-400 hover:text-red-600 dark:hover:text-red-400"
                  @click="confirmDelete(item)"
                  title="删除"
                >
                  <TrashIcon class="h-5 w-5" />
                </button>
                <button
                  v-if="item.type === 'directory'"
                  class="p-2 rounded-lg text-gray-400 hover:text-primary-600 dark:hover:text-primary-400"
                  @click.stop="showCopyModal = true; selectedDirectory = item"
                  title="复制目录"
                >
                  <DocumentDuplicateIcon class="h-5 w-5" />
                </button>
              </div>
            </div>
          </li>
        </ul>
      </div>
    </div>

    <!-- 新建文件夹模态框 -->
    <TransitionRoot appear :show="showNewFolderModal" as="template">
      <Dialog as="div" @close="showNewFolderModal = false" class="relative z-10">
        <TransitionChild
          enter="duration-300 ease-out"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="duration-200 ease-in"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <div class="fixed inset-0 bg-black bg-opacity-25" />
        </TransitionChild>

        <div class="fixed inset-0 overflow-y-auto">
          <div class="flex min-h-full items-center justify-center p-4">
            <TransitionChild
              enter="duration-300 ease-out"
              enter-from="opacity-0 scale-95"
              enter-to="opacity-100 scale-100"
              leave="duration-200 ease-in"
              leave-from="opacity-100 scale-100"
              leave-to="opacity-0 scale-95"
            >
              <DialogPanel class="w-full max-w-md transform overflow-hidden rounded-2xl bg-white dark:bg-gray-800 p-6 text-left align-middle shadow-xl transition-all">
                <DialogTitle as="h3" class="text-lg font-medium leading-6 text-gray-900 dark:text-white">
                  新建文件夹
                </DialogTitle>
                <div class="mt-4">
                  <input
                    type="text"
                    class="input"
                    placeholder="输入文件夹名称"
                    v-model="newFolderName"
                  />
                </div>
                <div class="mt-4 flex justify-end space-x-2">
                  <button class="btn-secondary" @click="showNewFolderModal = false">取消</button>
                  <button class="btn-primary" @click="createFolder">创建</button>
                </div>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </TransitionRoot>

    <!-- 添加加载状态显示 -->
    <div v-if="loading" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white dark:bg-gray-800 rounded-lg p-4 flex items-center space-x-2">
        <svg class="animate-spin h-5 w-5 text-primary-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        <span class="text-gray-900 dark:text-gray-100">处理中...</span>
      </div>
    </div>

    <!-- 修改复制模态框 -->
    <TransitionRoot appear :show="showCopyModal" as="template">
      <Dialog as="div" @close="showCopyModal = false" class="relative z-10">
        <TransitionChild
          enter="duration-300 ease-out"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="duration-200 ease-in"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <div class="fixed inset-0 bg-black bg-opacity-25" />
        </TransitionChild>

        <div class="fixed inset-0 overflow-y-auto">
          <div class="flex min-h-full items-center justify-center p-4">
            <DialogPanel class="w-full max-w-md transform overflow-hidden rounded-2xl bg-white dark:bg-gray-800 p-6 text-left align-middle shadow-xl transition-all">
              <DialogTitle as="h3" class="text-lg font-medium leading-6 text-gray-900 dark:text-white">
                复制目录内容
              </DialogTitle>
              <div class="mt-2">
                <p class="text-sm text-gray-500 dark:text-gray-400">
                  将 "{{ selectedDirectory?.name }}" 目录下的文件复制到新目录
                </p>
              </div>
              <div class="mt-4">
                <input
                  type="text"
                  class="input"
                  placeholder="输入新目录名称"
                  v-model="targetFolderName"
                />
              </div>
              <div class="mt-4 flex justify-end space-x-2">
                <button class="btn-secondary" @click="showCopyModal = false">取消</button>
                <button 
                  class="btn-primary"
                  @click="copyDirectory"
                  :disabled="!targetFolderName.trim()"
                >
                  复制
                </button>
              </div>
            </DialogPanel>
          </div>
        </div>
      </Dialog>
    </TransitionRoot>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { Dialog, DialogPanel, DialogTitle, TransitionRoot, TransitionChild } from '@headlessui/vue'
import {
  FolderIcon,
  DocumentIcon,
  TrashIcon,
  FolderPlusIcon,
  ArrowUpTrayIcon,
  ArrowDownTrayIcon,
  ChevronRightIcon,
  HomeIcon,
  FolderOpenIcon,
  MagnifyingGlassIcon,
  ArrowLeftIcon,
  CheckCircleIcon,
  DocumentDuplicateIcon
} from '@heroicons/vue/24/outline'
import { fileService } from '../../services/fileService'
import { ElMessage } from 'element-plus'

// 状态
const currentPath = ref('')
const items = ref([])
const searchQuery = ref('')
const showNewFolderModal = ref(false)
const newFolderName = ref('')

// 加载状态
const loading = ref(false)

// 添加选择状态
const selectedItems = ref(new Set())
const isSelecting = ref(false)

// 添加复制相关的状态
const showCopyModal = ref(false)
const targetFolderName = ref('')
const selectedDirectory = ref(null)

// 状态管理部分 - 将相关状态分组
const uiState = ref({
  loading: false,
  showNewFolderModal: false,
  showCopyModal: false,
  isSelecting: false
})

// 文件管理相关状态
const fileState = ref({
  currentPath: '',
  items: [],
  searchQuery: '',
  newFolderName: '',
  targetFolderName: '',
  selectedDirectory: null
})

// 新增：添加dirCounts和fetchDirCounts逻辑
const dirCounts = ref({})
// 统计当前页面每个目录的子项数量
const fetchDirCounts = async () => {
  const counts = {}
  for (const item of filteredItems.value) {
    if (item.type === 'directory') {
      try {
        const subItems = await fileService.listFiles(item.path)
        const fileCount = subItems.filter(i => i.type === 'file').length
        const dirCount = subItems.filter(i => i.type === 'directory').length
        counts[item.path] = { fileCount, dirCount }
      } catch (e) {
        counts[item.path] = { fileCount: 0, dirCount: 0 }
      }
    }
  }
  dirCounts.value = counts
}

// 辅助函数 - 获取项目的完整路径
const getItemPath = (item) => {
  return currentPath.value 
    ? `${currentPath.value}/${item.name}` 
    : item.name
}

// 计算属性优化
const pathParts = computed(() => {
  return currentPath.value.split('/').filter(Boolean)
})

const filteredItems = computed(() => {
  if (!searchQuery.value) return items.value
  
  const query = searchQuery.value.toLowerCase()
  return items.value.filter(item => 
    item.name.toLowerCase().includes(query)
  )
})

// 初始化加载文件列表
onMounted(async () => {
  await loadFiles()
})

// 加载文件列表 - 添加错误处理和重试机制
const loadFiles = async (retryCount = 1) => {
  try {
    loading.value = true
    uiState.value.loading = true
    
    // 使用currentPath而不是fileState.currentPath
    items.value = await fileService.listFiles(currentPath.value)
    
    // 同步更新fileState
    fileState.value.items = items.value
    fileState.value.currentPath = currentPath.value
    await fetchDirCounts() // 新增
  } catch (error) {
    console.error('加载文件列表失败:', error)
    
    // 添加重试逻辑
    if (retryCount > 0) {
      ElMessage.warning('加载失败，正在重试...')
      setTimeout(() => loadFiles(retryCount - 1), 1000)
      return
    }
    
    ElMessage.error('加载文件列表失败')
  } finally {
    loading.value = false
    uiState.value.loading = false
  }
}

const navigateBack = async () => {
  if (!currentPath.value) return
  const parts = currentPath.value.split('/')
  parts.pop() // 移除最后一个部分
  currentPath.value = parts.join('/')
  
  // 同步更新fileState中的currentPath
  fileState.value.currentPath = currentPath.value
  
  await loadFiles()
}

const navigateToRoot = async () => {
  if (!currentPath.value) return
  currentPath.value = ''
  
  // 同步更新fileState中的currentPath
  fileState.value.currentPath = currentPath.value
  
  await loadFiles()
}

const navigateTo = async (index) => {
  currentPath.value = pathParts.value.slice(0, index + 1).join('/')
  
  // 同步更新fileState中的currentPath
  fileState.value.currentPath = currentPath.value
  
  await loadFiles()
}

const handleFileUpload = async (event) => {
  const files = event.target.files
  if (!files.length) return

  try {
    loading.value = true
    for (const file of files) {
      // 直接使用当前路径上传文件
      const uploadPath = currentPath.value || ''
      await fileService.uploadFile(uploadPath, file)
      ElMessage.success(`文件 ${file.name} 上传成功`)
    }
    await loadFiles()
  } catch (error) {
    ElMessage.error('文件上传失败')
    console.error('文件上传失败:', error)
  } finally {
    loading.value = false
    event.target.value = '' // 重置文件输入
  }
}

const downloadFile = (file) => {
  const downloadUrl = fileService.getDownloadUrl(
    `${currentPath.value}/${file.name}`
  )
  window.open(downloadUrl, '_blank')
}

const confirmDelete = async (item) => {
  // 构建更详细的确认消息
  const isDirectory = item.type === 'directory'
  const confirmMessage = `确定要删除 ${item.name} 吗？${
    isDirectory 
      ? '这将删除该目录下的所有文件和子目录！此操作不可恢复！' 
      : '此操作不可恢复！'
  }`
  
  if (!confirm(confirmMessage)) return
  
  try {
    uiState.value.loading = true
    const path = getItemPath(item)
    
    if (isDirectory) {
      const result = await fileService.deleteDirectory(path)
      const deletedCount = result?.deletedCount || 1
      ElMessage.success(`删除成功！共删除 ${deletedCount} 个项目`)
    } else {
      await fileService.deleteFile(path)
      ElMessage.success('文件删除成功')
    }
    
    await loadFiles()
  } catch (error) {
    const errorMsg = error.message || '未知错误'
    console.error('删除失败详情:', error)
    ElMessage.error(`删除失败: ${errorMsg}`)
  } finally {
    uiState.value.loading = false
  }
}

// 批量删除方法也需要修改
const confirmBatchDelete = async () => {
  if (selectedItems.value.size === 0) return
  
  const dirCount = Array.from(selectedItems.value).filter(item => item.type === 'directory').length
  const fileCount = selectedItems.value.size - dirCount
  
  const confirmMessage = `确定要删除选中的 ${fileCount} 个文件${dirCount > 0 ? ` 和 ${dirCount} 个目录` : ''}吗？${dirCount > 0 ? '这将删除所有选中目录下的子文件和子目录！' : ''}`
  
  if (confirm(confirmMessage)) {
    try {
      loading.value = true
      
      // 逐个删除选中的项目
      let deletedCount = 0
      
      // 处理每个选中项
      for (const item of selectedItems.value) {
        const path = getItemPath(item) // 使用辅助函数获取路径
        
        if (item.type === 'directory') {
          // 直接删除目录
          await fileService.deleteDirectory(path)
          deletedCount++
        } else {
          // 删除文件
          await fileService.deleteFile(path)
          deletedCount++
        }
      }
      
      ElMessage.success(`批量删除成功！共删除 ${deletedCount} 个项目`)
      selectedItems.value.clear()
      isSelecting.value = false
      await loadFiles()
    } catch (error) {
      console.error('批量删除失败详情:', error)
      ElMessage.error(`批量删除失败: ${error.message || '未知错误'}`)
    } finally {
      loading.value = false
    }
  }
}

const createFolder = async () => {
  if (newFolderName.value.trim()) {
    try {
      loading.value = true
      // 直接导航到新文件夹
      currentPath.value = currentPath.value 
        ? `${currentPath.value}/${newFolderName.value.trim()}`
        : newFolderName.value.trim()
      
      showNewFolderModal.value = false
      newFolderName.value = ''
      ElMessage.success('文件夹创建成功')
      
      // 加载新路径（虽然可能为空）
      await loadFiles()
    } catch (error) {
      ElMessage.error('创建文件夹失败')
      console.error('创建文件夹失败:', error)
    } finally {
      loading.value = false
    }
  }
}

// 新增方法
const navigateToFolder = async (folder) => {
  // 使用当前路径变量，而不是fileState中的变量
  currentPath.value = currentPath.value ? `${currentPath.value}/${folder.name}` : folder.name
  
  // 同步更新fileState中的currentPath
  fileState.value.currentPath = currentPath.value
  
  await loadFiles()
}

const formatFileSize = (bytes) => {
  if (!bytes) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return `${(bytes / Math.pow(k, i)).toFixed(2)} ${sizes[i]}`
}

const previewFile = (file) => {
  // 根据文件类型处理预览
  const fileType = file.name.split('.').pop().toLowerCase()
  const imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'webp']
  
  if (imageTypes.includes(fileType)) {
    // 显示图片预览
    ElMessage.success('图片预览功能待实现')
  } else {
    // 直接下载
    downloadFile(file)
  }
}

// 选择相关方法
const toggleSelection = (item) => {
  if (selectedItems.value.has(item)) {
    selectedItems.value.delete(item)
  } else {
    selectedItems.value.add(item)
  }
}

const toggleSelectMode = () => {
  isSelecting.value = !isSelecting.value
  if (!isSelecting.value) {
    selectedItems.value.clear()
  }
}


// 复制目录方法
const copyDirectory = async () => {
  if (!targetFolderName.value.trim() || !selectedDirectory.value) return

  try {
    loading.value = true
    const sourcePath = currentPath.value 
      ? `${currentPath.value}/${selectedDirectory.value.name}`
      : selectedDirectory.value.name
    
    const newFolderPath = currentPath.value 
      ? `${currentPath.value}/${targetFolderName.value.trim()}`
      : targetFolderName.value.trim()

    const result = await fileService.copyDirectory(sourcePath, newFolderPath)

    // 处理部分成功的情况
    if (result.partialSuccess) {
      ElMessage({
        message: result.message,
        type: 'warning',
        duration: 5000
      })
      console.warn('部分文件复制失败:', result.failedItems)
    } else {
      ElMessage.success('目录复制成功')
    }
    
    showCopyModal.value = false
    targetFolderName.value = ''
    selectedDirectory.value = null
    await loadFiles()
  } catch (error) {
    // 提供更详细的错误信息
    const errorMsg = error.message || '目录复制失败'
    ElMessage({
      message: errorMsg,
      type: 'error',
      duration: 5000
    })
    console.error('目录复制失败:', error)
  } finally {
    loading.value = false
  }
}
</script>

<style>
.btn-danger {
  @apply btn bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
}
.dir-count-info {
  min-width: 70px;
  color: #9ca3af;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
}
.dir-count-info .icon {
  width: 14px;
  height: 14px;
  margin-right: 2px;
}
</style>