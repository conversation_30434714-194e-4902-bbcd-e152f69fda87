import { createRouter, createWebHistory } from 'vue-router'
import FileList from '../components/FileManager/FileList.vue'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: FileList
  },
  {
    path: '/game',
    name: 'Game',
    // 使用懒加载方式加载游戏管理页面
    component: () => import('../components/GameManager/GameDashboard.vue')
  },
  {
    path: '/editor',
    name: 'ModelEditor',
    // 使用懒加载方式加载模型编辑器页面
    component: () => import('../components/ModelEditor/ModelEditor.vue')
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router
