import axios from 'axios'

// 默认目录改为 website_public
const DEFAULT_DIRECTORY = 'website_public'

// 创建 axios 实例
const api = axios.create({
  baseURL: 'https://alpha-api-pre.satworld.io',
  // timeout: 10000,
  headers: {
    'admin_api_key': '549617ef-80dd-4589-9676-fec7d295e89c'
  }
})

// 处理响应数据的工具函数
const handleResponse = (response) => {
  if (response.data.code === 1) {
    return response.data.data
  }
  throw new Error(response.data.msg || '请求失败')
}

// 修改构建路径的函数
const buildPath = (path = '') => {
  if (!path) return DEFAULT_DIRECTORY
  return `${DEFAULT_DIRECTORY}/${path}`.replace(/\/+/g, '/')
}

// 修改文件列表处理函数
const extractDirectories = (files, currentPath) => {
  const directories = new Set()

  files.forEach(file => {
    const relativePath = file.key.replace(new RegExp(`^${DEFAULT_DIRECTORY}/?`), '')
    if (currentPath) {
      // 如果在子目录中，只处理当前路径下的内容
      if (!relativePath.startsWith(currentPath + '/')) {
        return
      }
      const subPath = relativePath.slice(currentPath.length + 1)
      const parts = subPath.split('/')
      if (parts.length > 1) {
        directories.add(parts[0])
      }
    } else {
      // 在根目录中
      const parts = relativePath.split('/')
      if (parts.length > 1) {
        directories.add(parts[0])
      }
    }
  })

  return Array.from(directories)
}

// 修改文件过滤函数
const filterCurrentFiles = (files, currentPath) => {
  return files.filter(file => {
    const relativePath = file.key.replace(new RegExp(`^${DEFAULT_DIRECTORY}/?`), '')
    if (currentPath) {
      // 在子目录中，只显示直接子文件
      return relativePath.startsWith(currentPath + '/') &&
          relativePath.slice(currentPath.length + 1).split('/').length === 1
    } else {
      // 在根目录中，只显示直接子文件
      return relativePath.split('/').length === 1
    }
  })
}

// 修改文件对象转换函数
const transformFileItem = (item) => {
  const fullPath = item.key
  const relativePath = fullPath.replace(new RegExp(`^${DEFAULT_DIRECTORY}/?`), '')
  const name = relativePath.split('/').pop() || relativePath

  return {
    name,
    type: 'file',
    size: parseInt(item.size || '0'),
    lastModified: item.lastModified,
    path: relativePath,
    fullPath: fullPath
  }
}

export const fileService = {
  async listFiles(path = '') {
    try {
      const fullPath = buildPath(path)
      // 添加时间戳参数
      const timestamp = new Date().getTime()
      const response = await api.get('/admin/obs/list', {
        params: { 
          key: fullPath,
          t: timestamp // 添加时间戳参数
         }
      })
      const data = handleResponse(response)

      // 获取当前目录下的文件和文件夹
      const currentFiles = filterCurrentFiles(data, path)
      const directories = extractDirectories(data, path)

      // 合并文件和文件夹列表
      const items = [
        // 添加文件夹
        ...directories.map(dirName => ({
          name: dirName,
          type: 'directory',
          size: 0,
          path: path ? `${path}/${dirName}` : dirName,
          fullPath: buildPath(path ? `${path}/${dirName}` : dirName)
        })),
        // 添加文件
        ...currentFiles.map(transformFileItem)
      ]

      return items
    } catch (error) {
      console.error('获取文件列表失败:', error)
      throw error
    }
  },

  async createFolder(path, name) {
    // 不需要实际创建文件夹，直接返回成功
    return { success: true }
  },

  async deleteFile(path) {
    try {
      const fullPath = buildPath(path)
      const response = await api.post('/admin/obs/delete', null, {
        params: { key: fullPath }
      })
      return handleResponse(response)
    } catch (error) {
      console.error('删除文件失败:', error)
      throw error
    }
  },

  async uploadFile(path, file) {
    try {
      // const timestamp = Date.now()
      // const fileName = `${timestamp}_${file.name}`
      // 只使用当前目录作为上传路径
      const uploadPath = buildPath(path)

      const formData = new FormData()
      formData.append('file', file)

      // 添加时间戳参数
      const timestamp = new Date().getTime()
      const response = await api.post('/admin/obs/upload', formData, {
        params: {
          key: uploadPath, // 只传递目录路径
          t: timestamp // 添加时间戳参数
        },
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
      return handleResponse(response)
    } catch (error) {
      console.error('上传文件失败:', error)
      throw error
    }
  },

  getDownloadUrl(path) {
    // 使用 static.satworld.io 域名构建下载链接，并添加时间戳
    const fullPath = buildPath(path)
    const timestamp = new Date().getTime() // 获取当前时间戳
    return `https://static.satworld.io/${fullPath}?t=${timestamp}`
  },

  // 下载文件内容
  async downloadFileContent(path) {
    try {
      const fullPath = buildPath(path)
      const timestamp = new Date().getTime() // 获取当前时间戳
      const url = `https://static.satworld.io/${fullPath}?t=${timestamp}`
      const response = await fetch(url)
      if (!response.ok) throw new Error('下载文件失败')
      const blob = await response.blob()
      return blob
    } catch (error) {
      console.error('下载文件内容失败:', error)
      throw error
    }
  },

  // 复制文件到新路径
  async copyFile(sourcePath, targetPath) {
    try {
      // 1. 下载源文件
      const fileBlob = await this.downloadFileContent(sourcePath)
      
      // 2. 从源路径中获取原始文件名
      const originalFileName = sourcePath.split('/').pop()
      
      // 3. 创建 FormData 并上传到新路径
      const formData = new FormData()
      const file = new File([fileBlob], originalFileName, { type: fileBlob.type })
      formData.append('file', file)

      // 添加时间戳参数
      const timestamp = new Date().getTime()
      const response = await api.post('/admin/obs/upload', formData, {
        params: { 
          key: buildPath(targetPath), // 使用目标路径，不再附加文件名
          t: timestamp // 添加时间戳参数
        },
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
      return handleResponse(response)
    } catch (error) {
      console.error('复制文件失败:', error)
      throw error
    }
  },

  // 复制目录
  async copyDirectory(sourcePath, targetPath) {
    try {
      // 1. 获取源目录下的所有文件
      const sourceFiles = await this.listFiles(sourcePath)
      
      // 2. 批量处理文件复制，每批最多处理10个文件
      const batchSize = 50
      const failedItems = []
      
      // 将文件分批处理
      for (let i = 0; i < sourceFiles.length; i += batchSize) {
        const batch = sourceFiles.slice(i, i + batchSize)
        const copyPromises = batch.map(file => {
          try {
            const sourceFilePath = sourcePath + '/' + file.name
            // 直接使用目标路径，不再附加文件名
            if (file.type === 'directory') {
              return this.copyDirectory(sourceFilePath, targetPath + '/' + file.name)
                .catch(err => {
                  failedItems.push({ path: sourceFilePath, error: err.message })
                  return null
                })
            }
            if (file.type === 'file') {
              return this.copyFile(sourceFilePath, targetPath)
                .catch(err => {
                  failedItems.push({ path: sourceFilePath, error: err.message })
                  return null
                })
            }
            return Promise.resolve(null)
          } catch (err) {
            failedItems.push({ path: file.name, error: err.message })
            return Promise.resolve(null)
          }
        })

        // 等待当前批次完成
        await Promise.all(copyPromises)
      }
      
      // 如果有失败项，返回部分成功信息
      if (failedItems.length > 0) {
        if (failedItems.length === sourceFiles.length) {
          throw new Error('所有文件复制失败')
        } else {
          return { 
            partialSuccess: true, 
            message: `成功复制${sourceFiles.length - failedItems.length}个文件，${failedItems.length}个文件失败`,
            failedItems 
          }
        }
      }
      
      return { success: true }
    } catch (error) {
      console.error('复制目录失败:', error)
      throw error
    }
  },

  // 修改删除目录方法
  async deleteDirectory(path) {
    try {
      // 1. 获取目录下的所有文件和子目录
      const files = await this.listFiles(path)
      
      // 2. 递归删除所有文件和子目录
      const deletePromises = files.map(file => {
        const filePath = path + '/' + file.name
        // 如果是目录，递归删除
        if (file.type === 'directory') {
          return this.deleteDirectory(filePath)
        }
        // 如果是文件，直接删除
        return this.deleteFile(filePath)
      })

      await Promise.all(deletePromises)
      return { success: true }
    } catch (error) {
      console.error('删除目录失败:', error)
      throw error
    }
  }
}